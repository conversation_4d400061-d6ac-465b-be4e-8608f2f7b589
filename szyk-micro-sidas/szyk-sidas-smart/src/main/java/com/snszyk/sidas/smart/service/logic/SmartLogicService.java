package com.snszyk.sidas.smart.service.logic;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.smart.client.SseEmitterClient;
import com.snszyk.sidas.smart.config.DifyProperties;
import com.snszyk.sidas.smart.constent.DifyConstent;
import com.snszyk.sidas.smart.enums.DifyMessageType;
import com.snszyk.sidas.smart.factory.RequestFactory;
import com.snszyk.sidas.smart.model.BaseRequest;
import com.snszyk.sidas.smart.model.ChatAssistantRequest;
import com.snszyk.sidas.smart.model.ChatflowRequest;
import com.snszyk.sidas.smart.util.RestTemplateUtil;
import com.snszyk.sidas.smart.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmartLogicService {
    private final RequestFactory requestFactory;
    private final DifyProperties difyProperties;

	public SseEmitter processRequest(MessageVo v) {
		log.info("dify请求接口：{}", JSON.toJSONString(difyProperties));
		DifyMessageType type = DifyMessageType.getByCode(v.getType());
		BaseRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(Func.toStr(AuthUtil.getUserId()))
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()));

		// 根据类型设置特定属性
		switch (type) {
			case CHAT_ASSISTANT:
				request.setUri(DifyConstent.CHAT_ASSISTANT_PATH);
				((ChatAssistantRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case CHATFLOW:
				request.setUri(DifyConstent.CHATFLOW_PATH);
				((ChatflowRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case WORKFLOW:
				request.setUri(DifyConstent.WORKFLOW_PATH);
				if (StringUtils.isNotBlank(v.getContent())) {
					request.getInputs().put("query", v.getContent());
				}
				break;
			default:
				log.warn("Unsupported message type: {}", type);
				break;
		}

		// 发送请求并处理响应
		return SseEmitterClient.createEmitter(request);
	}

	public JSONArray chatflowHistory(Map<String, Object> map) {
		log.info("获取dify通用问答流程历史会话记录：{}", JSON.toJSONString(map));
		try {
			Map<String,Object> params = new HashMap<>();
			params.put("user", Func.toStr(AuthUtil.getUserId()));
			if (map.containsKey("limit")) {
				params.put("limit", map.get("limit"));
			}
			if (map.containsKey("lastId")) {
				params.put("last_id", map.get("lastId"));
			}
			if (map.containsKey("sortBy")) {
				params.put("sort_by", map.get("sortBy"));
			}
			// 设置请求头
			Map<String, String> headers = new HashMap<>();
			headers.put("Authorization", "Bearer " + difyProperties.getApiKey().get(map.get("type").toString()));
			ResponseEntity<String> response = RestTemplateUtil.get(difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_HISTORY, params, headers, String.class);

			// 处理响应
			String responseBody = response.getBody();
			log.info("Dify历史会话记录响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);
			// 解析响应JSON
			return JSON.parseObject(responseBody).getJSONArray("data");
		} catch (Exception e) {
			log.error("获取Dify历史会话记录异常", e);
			throw new ServiceException("获取历史会话记录失败: " + e.getMessage());
		}
	}

	public JSONArray conversationMessage(Map<String, Object> map) {
		log.info("获取dify通用问答会话消息：{}", JSON.toJSONString(map));
		try {
			// 组装query参数
			Map<String,Object> params = new HashMap<>();
			params.put("user", Func.toStr(AuthUtil.getUserId()));
			params.put("conversation_id", map.get("conversationId"));
			if (map.containsKey("limit")) {
				params.put("limit", map.get("limit"));
			}
			if (map.containsKey("firstId")) {
				params.put("first_id", map.get("firstId"));
			}
			// 设置请求头
			Map<String, String> headers = new HashMap<>();
			headers.put("Authorization", "Bearer " + difyProperties.getApiKey().get(map.get("type").toString()));
			ResponseEntity<String> response = RestTemplateUtil.get(difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_MESSAGE, params, headers, String.class);
			// 处理响应
			String responseBody = response.getBody();
			log.info("Dify历史会话记录响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);
			return JSON.parseObject(responseBody).getJSONArray("data");
		} catch (Exception e) {
			log.error("获取Dify会话消息异常", e);
			throw new ServiceException("获取会话消息失败: " + e.getMessage());
		}
	}

	public List<String> nextStepSuggestion(Map<String, String> map) throws IOException {
		// 组装query参数
		Map<String, Object> params = new HashMap<>();
		params.put("user", Func.toStr(AuthUtil.getUserId()));

		// 组装Header
		Map<String, String> headers = new HashMap<>();
		headers.put("Authorization", "Bearer " + difyProperties.getApiKey().get(map.get("type")));
		headers.put("Content-Type", "application/json; charset=utf-8");

		String url = difyProperties.getBaseUrl() + DifyConstent.NEXT_STEP_SUGGESTION.replace("{message_id}", map.get("messageId"));
		ResponseEntity<String> response = RestTemplateUtil.get(url, params, headers, String.class);
		// 处理响应
		String responseBody = response.getBody();
		if (StringUtils.isNotBlank(responseBody)) {
			return JSON.parseObject(responseBody).getJSONArray("data").toJavaList(String.class);
		}
		return new ArrayList<>();
	}

	public boolean delConversation(Map<String, Object> map) {
		// 组装query参数
		Map<String, Object> params = new HashMap<>();
		params.put("user", Func.toStr(AuthUtil.getUserId()));
		// 组装Header
		Map<String, String> headers = new HashMap<>();
		headers.put("Authorization", "Bearer " + difyProperties.getApiKey().get(map.get("type")));
		headers.put("Content-Type", "application/json; charset=utf-8");
		ResponseEntity<String> response = RestTemplateUtil.get(String.format(DifyConstent.DEL_CONVERSATION, map.get("conversationId")), params, headers, String.class);
		// 处理响应
		String responseBody = response.getBody();
		log.info("Dify删除会话响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);
		return Boolean.TRUE;
	}
}
