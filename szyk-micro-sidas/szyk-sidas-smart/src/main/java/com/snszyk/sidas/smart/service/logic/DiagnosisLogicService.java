package com.snszyk.sidas.smart.service.logic;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.smart.client.WaveClient;
import com.snszyk.sidas.smart.config.WaveProperties;
import com.snszyk.sidas.smart.dto.*;
import com.snszyk.sidas.smart.mapper.EquipmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class DiagnosisLogicService {

	@Resource
    private EquipmentMapper equipmentMapper;
	@Resource
    private InfluxdbTools influxdbTools;
	@Resource
    private WaveProperties waveProperties;
	@Resource
    private WaveClient waveClient;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String START_DATE = "2025-01-01 00:00:00";
    private static final String END_DATE = "2025-06-04 23:59:59";
	private static final String EQUIPMENT_INFO = "【设备名称】：%s\n【部位信息】\n%s\n【部位测点路径】%s\n";
	private static final String ALARM_INFO = "【设备名称】：%s\n【部位报警信息】：%s\n";

    public String getEquipmentByMonitorId(Long monitorId) {
		MonitorDto monitorDto = equipmentMapper.getMonitorById(monitorId);
        return Optional.ofNullable(equipmentMapper.getEquipmentByMonitorId(monitorId))
                .map(dto -> {
					// 设备名称
					String deviceName = dto.getPathName().split(",")[dto.getPathName().split(",").length - 2];
					StringBuilder eqBuilder = new StringBuilder();
					dto.convertLLmPrompt(eqBuilder);
					return String.format(EQUIPMENT_INFO, deviceName, eqBuilder.toString(), monitorDto.getPathName().replaceAll(",", "/"));
				})
                .orElse("");
    }

    private Long dateConvert(String date) {
        return LocalDateTime.parse(date, DATE_FORMATTER)
                .toInstant(ZoneOffset.UTC)
                .toEpochMilli();
    }
	/**
	 * 获取波形信息
	 *
	 * @param deviceId 设备ID
	 * @param equipmentIds 设备-部位ids
	 * @param desc 描述信息
	 * @param inputs 输入参数Map
	 * @throws IOException 当波形处理发生IO异常时
	 */
	public void waveInfo(Long deviceId, List<Long> equipmentIds, String desc, Map<String, Object> inputs, HttpServletRequest request) throws IOException {
		String origin = request.getHeader("Origin");
		log.info("请求来源：{}", origin);

		// 获取设备信息
		List<EquipmentDto> equipments = equipmentMapper.getEquipmentInfo(equipmentIds, deviceId);
		if (Func.isEmpty(equipments)) {
			return;
		}
		EquipmentDto equipment = equipments.get(0);
		// 设备名称
		String deviceName = equipment.getPathName().split(",")[equipment.getPathName().split(",").length - 2];
		// 获取监测点列表
		List<MonitorDto> monitorList = new ArrayList<>();
		List<Long> ids = new ArrayList<>();

		StringBuilder eqBuilder = new StringBuilder();
		for (int i = 0; i < equipments.size(); i++) {
			EquipmentDto dto = equipments.get(i);
			eqBuilder.append("部件").append((i + 1));
			dto.convertLLmPrompt(eqBuilder);
			eqBuilder.append("\n");

			List<MonitorDto> monitorDtos = equipmentMapper.getMonitorList(dto.getId());
			if (CollectionUtils.isNotEmpty(monitorDtos)) {
				monitorList.addAll(monitorDtos);
			}

			ids.add(dto.getId());
		}

		Map<String, MonitorDto> waveformMap = new HashMap<>();
		AtomicReference<BigDecimal> samplingFrequency = new AtomicReference<>(BigDecimal.ZERO);
		StringBuilder monitorBuilder = new StringBuilder();
		// 处理每个监测点的波形数据
		monitorList.forEach(monitorDto -> {
			monitorBuilder.append(monitorDto.getPathName().replaceAll(",", "/"));
			processMonitorWaveforms(monitorDto, waveformMap, samplingFrequency);
		});

		// 构建波形提示
		StringBuilder wavePrompt = new StringBuilder("波形图与部位对应关系：");

		// 图片信息
		List<DiagnosisWaveDto> picList = new ArrayList<>();

		// 处理波形数据
		List<String> waveFiles = processWaveforms(new ArrayList<>(waveformMap.keySet()),
			waveformMap, samplingFrequency.get(), wavePrompt, origin, picList);

		String s = String.format(EQUIPMENT_INFO, deviceName, eqBuilder.toString(), monitorBuilder.toString());
		// 故障现象（非必填）
		if (Func.isNotEmpty(desc)) {
			s = s + "【故障现象】&emsp;&emsp;" + desc;
		}
		// 更新输入参数
		updateInputs(inputs, Func.join(ids, ","), s, waveFiles, wavePrompt, picList);
	}

	/**
	 * 处理监测点的波形数据
	 */
	private void processMonitorWaveforms(MonitorDto monitor, Map<String, MonitorDto> waveformMap,
										 AtomicReference<BigDecimal> samplingFrequency) {
		List<WaveDto> waves = equipmentMapper.waveList(monitor.getId());
		if (CollectionUtils.isEmpty(waves)) {
			return;
		}

		WaveDto latestWave = waves.get(0);
		samplingFrequency.set(latestWave.getSamplingFreq());

		JSONObject waveform = mainWaveForm(monitor.getId(), latestWave.getId());
		System.out.println("波形数据：" + waveform.toJSONString());
		if (waveform != null && waveform.getString("waveformUrl") != null) {
			String waveformUrl = waveProperties.getFileUrl() + waveform.getString("waveformUrl");
			waveformMap.put(waveformUrl, monitor);
		}
	}

	/**
	 * 处理波形数据并获取波形文件列表
	 */
	private List<String> processWaveforms(List<String> waveUrls, Map<String, MonitorDto> waveformMap, BigDecimal samplingFrequency,
										  StringBuilder wavePrompt, String origin, List<DiagnosisWaveDto> picList) throws IOException {
		long startTime = System.currentTimeMillis();
		String result = waveClient.post(
			waveProperties.getUrl() + waveProperties.getWaveRoute(),
			waveClient.buildJsonBody(waveUrls, samplingFrequency)
		);
		logExecutionTime(startTime);

		List<String> waveFiles = new ArrayList<>();
		if (result != null) {
			List<String> resultList = JSON.parseArray(result, String.class);
			int existPic = 0;
			for (int i = 0; i < resultList.size(); i++) {
				if (StringUtils.isNotBlank(resultList.get(i))) {
					existPic++;
					MonitorDto dto = waveformMap.get(waveUrls.get(i));
					String picActualPath = origin + resultList.get(i);
					joinStr(wavePrompt, dto, existPic);
					waveFiles.add(picActualPath);
					DiagnosisWaveDto diagnosisWaveDto = new DiagnosisWaveDto(dto.getName(), picActualPath);
					picList.add(diagnosisWaveDto);
				}
			}
		}
		return waveFiles;
	}

	/**
	 * 拼接监测点信息字符串
	 *
	 * @param sb 字符串构建器
	 * @param dto 监测点DTO
	 * @param index 图片序号
	 * @return 拼接后的字符串构建器
	 */
	private StringBuilder joinStr(StringBuilder sb, MonitorDto dto, int index) {
		// 将dto.pathName字符串中的英文逗号替换为斜杠且只取后三个
		String[] pathNames = dto.getPathName().split(",");
		String monitorPathName = String.join("/", Arrays.copyOfRange(pathNames, pathNames.length - 3, pathNames.length));

		return sb.append("<br>图")
			.append(index)
			.append("是")
			.append(monitorPathName)
			.append("测点的波形图");
	}

	/**
	 * 更新输入参数Map
	 */
	private void updateInputs(Map<String, Object> inputs, String equipmentId, String equipmentInfo,
							  List<String> waveFiles, StringBuilder wavePrompt, List<DiagnosisWaveDto> picList) {
		inputs.put("equipment", equipmentInfo);
		inputs.put("equipmentId", equipmentId);
		inputs.put("waveFiles", generateDifyFiles(waveFiles));
		inputs.put("wavePrompt", wavePrompt.toString());
		inputs.put("picList", JSON.toJSONString(picList));
	}

	/**
	 * 生成波形文件的JSON数组
	 */
	private JSONArray generateDifyFiles(List<String> waveFiles) {
		JSONArray array = new JSONArray();
		waveFiles.forEach(fileUrl -> {
			JSONObject obj = new JSONObject();
			obj.put("type", "image");
			obj.put("transfer_method", "remote_url");
			obj.put("url", fileUrl);
			array.add(obj);
		});
		return array;
	}

	/**
	 * 获取设备报警信息
	 *
	 * @param equipmentId 设备ID
	 * @return 报警信息的LLM提示字符串
	 */
	public String equipmentAlarm(String equipmentId) {
		List<Long> ids = Func.toLongList(",", equipmentId);
		EquipmentDto dto = equipmentMapper.getEquipment(ids.get(0));
		String deviceName = dto.getPathName().split(",")[dto.getPathName().split(",").length - 2];
		StringBuilder builder = new StringBuilder();
		for (Long id : ids) {
			DiagnosisRecordDto alarmDto = equipmentMapper.equipmentAlarm(id);
			if (alarmDto != null) {
				alarmDto.convertLLmPrompt(builder);
				// 查询报警时间
				Map<String, String> map = equipmentMapper.getAlarmTime(alarmDto.getAlarmId());
				if (map != null) {
					builder.append("，首次报警时间：").append(map.get("firstAlarmTime"));
					builder.append("，最新报警时间：").append(map.get("lastAlarmTime"));
				}
				builder.append("\n");
			}
		}
		return String.format(ALARM_INFO, deviceName, builder.toString());
	}

	/**
	 * 获取主波形数据
	 *
	 * @param monitorId 监测点ID
	 * @param waveId 波形ID
	 * @return 波形数据JSON对象
	 */
	public JSONObject mainWaveForm(Long monitorId, Long waveId) {
		JSONObject jsonObject = initParam(monitorId, waveId);

		List<JSONObject> list = influxdbTools.queryDataTable(
			dateConvert(START_DATE),
			dateConvert(END_DATE),
			jsonObject,
			null,
			(sql) -> {
				sql.getQuerySQL().append(" |>sort(columns: [\"_time\"], desc: true) ").append(" |>limit(n: 1000)");
				sql.addSort("_time", false).configLimit(1000);
			},
			(item) -> {
				JSONObject object = new JSONObject();
				object.put("waveId", waveId);
				object.put("sampleDataType", item.getValueByKey("sampleDataType"));
				object.put("monitorId", item.getValueByKey("monitorId"));
				object.put("originTime", item.getTime().toEpochMilli());
				object.put("value", item.getValueByKey("value"));
				object.put("waveformUrl", item.getValueByKey("waveformUrl"));
				return object;
			});
		return list.isEmpty() ? null : list.get(0);
	}

	/**
	 * 记录执行时间日志
	 */
	private void logExecutionTime(long startTime) {
		double executionTimeSeconds = (System.currentTimeMillis() - startTime) / 1000.0;
		log.info("执行耗时：{}s", executionTimeSeconds);
	}

	private JSONObject initParam(Long monitorId, Long waveId){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("monitorId", monitorId);
		jsonObject.put("waveId", waveId);
		jsonObject.put("invalid", "0");
		jsonObject.put("showWave", 1);
		jsonObject.put("startTime", START_DATE);
		jsonObject.put("endTime", END_DATE);
		jsonObject.put("hasWaveData", 1);
		return jsonObject;
	}
}
